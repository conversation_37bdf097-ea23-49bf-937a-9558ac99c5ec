using System.Linq.Expressions;
using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Shared.Utils;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.KanbanColumn.Commands;
using TeamFlow.Tasks.Application.Errors;
using TeamFlow.Tasks.Application.Extensions;
using TeamFlow.Tasks.Core.Abstractions.Repositories;
using TeamFlow.Tasks.Core.Abstractions.Services;
using TeamFlow.Tasks.Core.Entities;
using TaskEntity = TeamFlow.Tasks.Core.Entities.Task;

namespace TeamFlow.Tasks.Application.Features.KanbanColumns.Commands;

public sealed class UpdateKanbanColumnCommandHandler : IRequestHandler<UpdateKanbanColumnCommand, Result<Guid>>
{
    private readonly IRepository<KanbanColumn, Guid> _columnsRepository;
    private readonly IRepository<TaskEntity, Guid> _tasksRepository;
    private readonly ITaskStatusTracker _taskStatusTracker;
    private readonly ILogger<UpdateKanbanColumnCommandHandler> _logger;
    private readonly IValidator<UpdateKanbanColumnCommand> _validator;

    public UpdateKanbanColumnCommandHandler(
        IRepository<KanbanColumn, Guid> columnsRepository,
        IRepository<TaskEntity, Guid> tasksRepository,
        ITaskStatusTracker taskStatusTracker,
        ILogger<UpdateKanbanColumnCommandHandler> logger,
        IValidator<UpdateKanbanColumnCommand> validator)
    {
        _columnsRepository = columnsRepository;
        _tasksRepository = tasksRepository;
        _taskStatusTracker = taskStatusTracker;
        _logger = logger;
        _validator = validator;
    }

    public async Task<Result<Guid>> Handle(UpdateKanbanColumnCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Обновление колонки с ID: {Id}", request.Id);

        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            _logger.LogValidationErrors(validationResult.Errors, "обновлении колонки");
            return Result.Fail(ErrorsFactory.FromValidationResult("KanbanColumn", validationResult));
        }

        var column = await _columnsRepository.GetOneAsync(x => x.Id == request.Id, cancellationToken);

        if (column is null)
        {
            _logger.LogWarning("Колонка с идентификатором {Id} не найдена для обновления", request.Id);
            return Result.Fail(ErrorsFactory.NotFound(nameof(KanbanColumn), request.Id));
        }

        var newName = ValueComparer.GetNewValueIfUpdated(column.Name, request.Name);
        var newOrder = ValueComparer.GetNewValueIfUpdated(column.Order, request.Order);
        var newStatus = ValueComparer.GetNewValueIfUpdated(column.Status, request.Status);

        var isNameUpdated = newName is not null;
        var isOrderUpdated = newOrder is not null;
        var isStatusUpdated = newStatus is not null;

        if (isNameUpdated || isOrderUpdated)
        {
            _logger.LogInformation(
                "Проверка уникальности обновляемых полей колонки {Id}. Имя: {IsNameUpdated}, Порядок: {IsOrderUpdated}",
                request.Id, isNameUpdated, isOrderUpdated);

            Expression<Func<KanbanColumn, bool>> filter = c =>
                ((isNameUpdated && c.Name == request.Name)
                 || (isOrderUpdated && c.Order == request.Order))
                && c.Id != request.Id;

            var isExists = await _columnsRepository.FindAnyAsync(filter, cancellationToken);

            if (isExists)
            {
                _logger.LogWarning("Колонка с такими параметрами уже существует. Поле: {Field}, Значение: {Value}",
                    isNameUpdated ? "Name" : "Order",
                    isNameUpdated ? request.Name! : request.Order.ToString()!);

                return Result.Fail(ErrorsFactory.AlreadyExists(nameof(KanbanColumn),
                    isNameUpdated ? "Name" : "Order",
                    isNameUpdated ? request.Name! : request.Order.ToString()!));
            }
        }

        column.Name = newName ?? column.Name;
        column.Order = newOrder ?? column.Order;
        column.Status = newStatus ?? column.Status;

        // If status is updated, synchronize all tasks in this column
        if (isStatusUpdated)
        {
            _logger.LogInformation("Синхронизация статуса задач в колонке {ColumnId} с новым статусом {NewStatus}",
                column.Id, column.Status);

            var tasksInColumn = (await _tasksRepository.GetManyAsync(
                t => t.KanbanColumnId == column.Id,
                cancellationToken: cancellationToken)).ToList();

            foreach (var task in tasksInColumn.Where(task => task.Status != column.Status))
            {
                _logger.LogInformation("Обновление статуса задачи {TaskId} с {OldStatus} на {NewStatus}",
                    task.Id, task.Status, column.Status);
                _taskStatusTracker.TrackTimeAndStatusChange(task, column.Status);
            }

            if (tasksInColumn.Count != 0)
            {
                await _tasksRepository.BulkUpdateAsync(tasksInColumn.ToList(), cancellationToken);
                _logger.LogInformation("Обновлено {TaskCount} задач в колонке {ColumnId}",
                    tasksInColumn.Count(), column.Id);
            }
        }

        await _columnsRepository.UpdateAsync(column, cancellationToken);
        _logger.LogInformation("Колонка с ID {Id} успешно обновлена", request.Id);
        return Result.Ok(column.Id);
    }
}