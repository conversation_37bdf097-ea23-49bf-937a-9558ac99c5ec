using TeamFlow.Shared.Repositories.Entities;
using TeamFlow.Tasks.Core.Shared.Enums;

namespace TeamFlow.Tasks.Core.Entities;

public class KanbanColumn : Entity<Guid>
{
    public required Guid ProjectId { get; set; }
    
    public required string Name { get; set; }
    
    public required TaskStatuses Status { get; set; }

    public int Order { get; set; } = 0;

    // public int? TaskLimit { get; set; } = 1000;
    
    // public string Color { get; set; } = "White";
}