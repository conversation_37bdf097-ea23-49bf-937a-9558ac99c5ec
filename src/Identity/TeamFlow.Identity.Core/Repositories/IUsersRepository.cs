using TeamFlow.Identity.Core.Entities;

namespace TeamFlow.Identity.Core.Repositories;

public interface IUsersRepository : IGenericRepository<User, Guid>
{
    /// <summary>
    /// Асинхронно получает пользователя по ID с загрузкой всех связанных данных для профиля
    /// </summary>
    /// <param name="userId">Идентификатор пользователя</param>
    /// <param name="cancellationToken">Токен отмены</param>
    /// <returns>Пользователь со всеми связанными данными или null</returns>
    Task<User?> GetUserWithProfileDataAsync(Guid userId, CancellationToken cancellationToken = default);
}
