using TeamFlow.Identity.Core.Enums;
using TeamFlow.Shared.Repositories.Entities;

namespace TeamFlow.Identity.Core.Entities;

public class UserSkill
{
    public Guid UserId { get; set; }

    public Guid SkillId { get; set; }

    public SkillLevel Level { get; set; } = SkillLevel.Beginner;

    public int YearsExperience { get; set; }

    public bool IsPrimary { get; set; }

    public User User { get; set; }

    public Skill Skill { get; set; }
}