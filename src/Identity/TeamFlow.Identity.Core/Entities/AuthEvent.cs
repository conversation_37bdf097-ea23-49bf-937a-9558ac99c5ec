using System.ComponentModel.DataAnnotations;
using TeamFlow.Identity.Core.Enums;
using TeamFlow.Identity.Core.Enums.Jwt;
using TeamFlow.Shared.Repositories.Entities;

namespace TeamFlow.Identity.Core.Entities;

public class AuthEvent : Entity<Guid>
{
    public const byte MaxIpAddressLength = 45;
    public const byte MaxCountryLength = 2;
    public const byte MaxCityLength = 100;
    public const byte MaxDeviceTypeLength = 50;
    public const byte MaxDeviceNameLength = 100;
    public const byte MaxOsNameLength = 50;
    public const byte MaxOsVersionLength = 20;
    public const byte MaxBrowserNameLength = 50;
    public const byte MaxBrowserVersionLength = 20;
    
    // public Guid UserId { get; set; }
    public Guid SessionId { get; set; } 
        
    public AuthEventType EventType { get; set; }
    
    // IPv6 поддержка
    [MaxLength(MaxIpAddressLength)]
    public string? IpAddress { get; set; }
    
    [MaxLength(MaxCountryLength)]
    public string? Country { get; set; } // RU, US
    
    [MaxLength(MaxCityLength)]
    public string? City { get; set; }
    public DeviceKind DeviceType { get; set; } = DeviceKind.Unknown; // mobile, desktop, tablet
    
    [MaxLength(MaxDeviceNameLength)]
    public string? DeviceName { get; set; } // "iPhone 15", "Chrome on Windows"
    
    [MaxLength(MaxOsNameLength)]
    public string? OsName { get; set; } // iOS, Windows, Android, macOS
    
    [MaxLength(MaxOsVersionLength)]
    public string? OsVersion { get; set; } // 17.0, Windows 11
    
    [MaxLength(MaxBrowserNameLength)]
    public string? BrowserName { get; set; } // Chrome, Safari, Firefox
    
    [MaxLength(MaxBrowserVersionLength)]
    public string? BrowserVersion { get; set; } // 120
    
    public bool IsSuccessful { get; set; }
    public string? FailureReason { get; set; }
    
    // public Dictionary<string, object>? Metadata { get; set; }
    
    // public User User { get; set; }
    public UserSession Session { get; set; }
}