using Microsoft.EntityFrameworkCore;
using TeamFlow.Identity.Core.Entities;
using TeamFlow.Identity.Core.Repositories;
using TeamFlow.Identity.Persistence.Database;

namespace TeamFlow.Identity.Persistence.Repositories;

public class UsersRepository : GenericRepository<User, Guid>, IUsersRepository
{
    public UsersRepository(ApplicationDbContext context) : base(context)
    {
    }

    public async Task<User?> GetUserWithProfileDataAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await Context.Set<User>()
            .Include(u => u.Position)
            .Include(u => u.Settings)
            .Include(u => u.UserSkills)!
            .ThenInclude(us => us.Skill)
            .Include(u => u.Sessions)
            .ThenInclude(s => s.AuthEvents)
            .FirstOrDefaultAsync(u => u.Id == userId, cancellationToken);
    }
}