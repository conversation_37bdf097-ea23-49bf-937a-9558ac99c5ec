using System.Linq.Expressions;
using Microsoft.EntityFrameworkCore;
using TeamFlow.Identity.Core.Repositories;
using TeamFlow.Identity.Persistence.Database;
using TeamFlow.Shared.Contracts.Enums;
using TeamFlow.Shared.Contracts.Pagination;
using TeamFlow.Shared.Contracts.Sortin;
using TeamFlow.Shared.Repositories.Entities;
using TeamFlow.Shared.Repositories.Repositories.Common;

namespace TeamFlow.Identity.Persistence.Repositories;

public class GenericRepository<TEntity, TKey> : IGenericRepository<TEntity, TKey>
    where TKey : IEquatable<TKey> where TEntity : class, IEntity<TKey>
{
    protected readonly ApplicationDbContext Context;
    
    private const int BatchSize = 1000; 

    public GenericRepository(ApplicationDbContext context)
    {
        Context = context;
    }

    public virtual async Task<TEntity?> GetOneAsync(Expression<Func<TEntity, bool>> predicate,
        CancellationToken cancellationToken = default)
        => await Context.Set<TEntity>().FirstOrDefaultAsync(predicate, cancellationToken);

    public virtual async Task<TEntity?> GetOneWithIncludesAsync(
        Expression<Func<TEntity, bool>> predicate,
        CancellationToken cancellationToken = default,
        params Expression<Func<TEntity, object>>[] includes)
    {
        var query = Context.Set<TEntity>().AsQueryable();

        // Apply includes
        foreach (var include in includes)
        {
            query = query.Include(include);
        }

        return await query.FirstOrDefaultAsync(predicate, cancellationToken);
    }

    public virtual async Task<IEnumerable<TEntity>> GetManyAsync(Expression<Func<TEntity, bool>>? predicate = null,
        OrderByExpression<TEntity>? sortExpression = null,
        CancellationToken cancellationToken = default)
    {
        var query = Context.Set<TEntity>().AsQueryable();

        if (predicate is not null)
        {
            query = query.Where(predicate);
        }

        if (sortExpression is not null)
        {
            query = ApplySort(query, sortExpression);
        }

        return await query.ToListAsync(cancellationToken);
    }

    public virtual async Task<PagedResult<TEntity>> GetManyAsync(Pageable pageable,
        Expression<Func<TEntity, bool>>? predicate = null,
        OrderByExpression<TEntity>? sortExpression = null,
        CancellationToken cancellationToken = default)
    {
        int pageNumber = Math.Max(1, pageable.PageNumber);
        int pageSize = Math.Clamp(pageable.PageSize, 1, 1000);

        var query = Context.Set<TEntity>().AsQueryable();

        if (predicate is not null)
        {
            query = query.Where(predicate);
        }

        var totalCount = await query.CountAsync(cancellationToken);

        if (sortExpression is not null)
        {
            query = ApplySort(query, sortExpression);
        }

        var elements = await query
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return new PagedResult<TEntity>(elements, totalCount);
    }

    public virtual async Task AddAsync(TEntity entity, CancellationToken cancellationToken = default)
    {
        await Context.Set<TEntity>().AddAsync(entity, cancellationToken);
        await Context.SaveChangesAsync(cancellationToken);
    }

    public virtual async Task UpdateAsync(TEntity entity, CancellationToken cancellationToken = default)
    {
        Context.Set<TEntity>().Update(entity);
        await Context.SaveChangesAsync(cancellationToken);
    }

    public virtual async Task RemoveAsync(TEntity entity, CancellationToken cancellationToken = default)
    {
        Context.Set<TEntity>().Remove(entity);
        await Context.SaveChangesAsync(cancellationToken);
    }

    public virtual Task<bool> FindAnyAsync(Expression<Func<TEntity, bool>> predicate,
        CancellationToken cancellationToken = default)
    {
        return Context.Set<TEntity>().AnyAsync(predicate, cancellationToken);
    }

    public virtual async Task BulkInsertAsync(IEnumerable<TEntity> entities, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(entities, nameof(entities));

        var list = entities.ToList();
        if (list.Count == 0)
        {
            return;
        }

        await Context.Set<TEntity>().AddRangeAsync(list, cancellationToken);
        await Context.SaveChangesAsync(cancellationToken);
    }

    public virtual async Task BulkUpdateAsync(IEnumerable<TEntity> entities, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(entities, nameof(entities));

        var list = entities.ToList();
        if (list.Count == 0)
        {
            return;
        }

        Context.Set<TEntity>().UpdateRange(list);
        await Context.SaveChangesAsync(cancellationToken);
    }
    
    public virtual async Task BulkDeleteAsync(IEnumerable<TKey> ids, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(ids, nameof(ids));

        var list = ids.ToList();

        if (list.Count == 0)
        {
            return;
        }
        
        //TODO Вынести в параметры куда-то
        await Context.Set<TEntity>()
            .Where(x => list.Contains(x.Id))
            //Batch size
            .Take(BatchSize) 
            .ExecuteDeleteAsync(cancellationToken);
    }

    public virtual async Task BulkDeleteAsync(Expression<Func<TEntity, bool>> predicate,
        CancellationToken cancellationToken = default)
    {
        await Context.Set<TEntity>()
            .Where(predicate)
            .Take(BatchSize)
            .ExecuteDeleteAsync(cancellationToken);
    }

    public async Task<ITransaction> BeginTransactionAsync(CancellationToken cancellationToken = default)
        => new Transaction(await Context.Database.BeginTransactionAsync(cancellationToken));

    private static IQueryable<TEntity> ApplySort(IQueryable<TEntity> query, OrderByExpression<TEntity> sortExpression)
    {
        ArgumentNullException.ThrowIfNull(query, nameof(query));
        ArgumentNullException.ThrowIfNull(sortExpression, nameof(sortExpression));

        return sortExpression.Direction switch
        {
            SortDirection.Ascending => query.OrderBy(sortExpression.KeySelector),
            SortDirection.Descending => query.OrderByDescending(sortExpression.KeySelector),
            _ => query.OrderBy(sortExpression.KeySelector)
        };
    }
}