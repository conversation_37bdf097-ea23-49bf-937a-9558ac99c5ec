using TeamFlow.Identity.Core.Enums;

namespace TeamFlow.Identity.Application.Contracts.Dto.ViewModels;

public record UserSkillViewModel
{
    public required Guid SkillId { get; init; }
    public required string Name { get; init; }
    public required string Category { get; init; }
    public required SkillLevel Level { get; init; }
    public required int YearsExperience { get; init; }
    public required bool IsPrimary { get; init; }
}